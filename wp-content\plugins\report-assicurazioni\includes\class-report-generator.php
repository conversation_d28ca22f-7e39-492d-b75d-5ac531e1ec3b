<?php
/**
 * Report Generator for Report Assicurazioni plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Report_Assicurazioni_Report_Generator {

    /**
     * Get orders containing products from Assicurazioni category
     */
    public function get_orders($start_date = '', $end_date = '', $search = '') {
        // Get the Assicurazioni category
        $assicurazioni_category = $this->get_assicurazioni_category();
        
        if (!$assicurazioni_category) {
            return array();
        }

        // Get products in the Assicurazioni category
        $assicurazioni_products = $this->get_products_in_category($assicurazioni_category->term_id);
        
        if (empty($assicurazioni_products)) {
            return array();
        }

        // Build query arguments
        $args = array(
            'limit' => -1,
            'status' => array('wc-completed', 'wc-processing', 'wc-on-hold'),
            'meta_query' => array(),
        );

        // Add date range filter
        if (!empty($start_date) && !empty($end_date)) {
            $args['date_created'] = $start_date . '...' . $end_date;
        } elseif (!empty($start_date)) {
            $args['date_created'] = '>=' . $start_date;
        } elseif (!empty($end_date)) {
            $args['date_created'] = '<=' . $end_date;
        }

        // Get all orders
        $orders = wc_get_orders($args);
        
        // Filter orders that contain Assicurazioni products
        $filtered_orders = array();
        
        foreach ($orders as $order) {
            $has_assicurazioni_product = false;
            
            foreach ($order->get_items() as $item) {
                $product_id = $item->get_product_id();
                $variation_id = $item->get_variation_id();
                
                // Check if product or variation is in Assicurazioni category
                if (in_array($product_id, $assicurazioni_products) || 
                    ($variation_id && in_array($variation_id, $assicurazioni_products))) {
                    $has_assicurazioni_product = true;
                    break;
                }
            }
            
            if ($has_assicurazioni_product) {
                // Apply search filter if provided
                if (!empty($search)) {
                    $search_fields = array(
                        $order->get_order_number(),
                        $order->get_billing_first_name(),
                        $order->get_billing_last_name(),
                        $order->get_billing_email(),
                    );
                    
                    // Add product names to search
                    foreach ($order->get_items() as $item) {
                        $product = $item->get_product();
                        if ($product) {
                            $search_fields[] = $product->get_name();
                        }
                    }
                    
                    $search_string = implode(' ', $search_fields);
                    
                    if (stripos($search_string, $search) !== false) {
                        $filtered_orders[] = $order;
                    }
                } else {
                    $filtered_orders[] = $order;
                }
            }
        }

        // Sort orders by date (newest first)
        usort($filtered_orders, function($a, $b) {
            return $b->get_date_created()->getTimestamp() - $a->get_date_created()->getTimestamp();
        });

        return $filtered_orders;
    }

    /**
     * Get the Assicurazioni product category
     */
    private function get_assicurazioni_category() {
        $category = get_term_by('name', 'Assicurazioni', 'product_cat');
        
        if (!$category) {
            // Try with slug
            $category = get_term_by('slug', 'assicurazioni', 'product_cat');
        }
        
        return $category;
    }

    /**
     * Get all products in a specific category
     */
    private function get_products_in_category($category_id) {
        $args = array(
            'post_type' => array('product', 'product_variation'),
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id,
                    'include_children' => true,
                ),
            ),
        );

        $products = get_posts($args);
        
        return $products;
    }

    /**
     * Get report data for export
     */
    public function get_report_data($start_date = '', $end_date = '', $search = '') {
        $orders = $this->get_orders($start_date, $end_date, $search);
        $data = array();

        foreach ($orders as $order) {
            $products = array();
            foreach ($order->get_items() as $item) {
                $product = $item->get_product();
                if ($product) {
                    $products[] = $product->get_name() . ' (x' . $item->get_quantity() . ')';
                }
            }

            $data[] = array(
                'order_number' => $order->get_order_number(),
                'order_date' => $order->get_date_created()->date_i18n(get_option('date_format')),
                'products' => implode(', ', $products),
                'total' => $order->get_total(),
                'formatted_total' => $order->get_formatted_order_total(),
            );
        }

        return $data;
    }

    /**
     * Get summary statistics
     */
    public function get_summary_stats($start_date = '', $end_date = '', $search = '') {
        $orders = $this->get_orders($start_date, $end_date, $search);
        
        $total_orders = count($orders);
        $total_amount = 0;
        $total_products = 0;

        foreach ($orders as $order) {
            $total_amount += $order->get_total();
            $total_products += count($order->get_items());
        }

        return array(
            'total_orders' => $total_orders,
            'total_amount' => $total_amount,
            'formatted_total_amount' => wc_price($total_amount),
            'total_products' => $total_products,
            'average_order_value' => $total_orders > 0 ? $total_amount / $total_orders : 0,
            'formatted_average_order_value' => $total_orders > 0 ? wc_price($total_amount / $total_orders) : wc_price(0),
        );
    }
}
