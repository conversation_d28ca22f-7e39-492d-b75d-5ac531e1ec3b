<?php
/**
 * PDF Exporter for Report Assicurazioni plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Report_Assicurazioni_PDF_Exporter {

    /**
     * Export report to PDF
     */
    public function export($start_date = '', $end_date = '', $search = '') {
        // Check if we can use a PDF library
        if (!$this->check_pdf_support()) {
            wp_die(__('PDF export is not available. Please install a PDF library.', 'report-assicurazioni'));
        }

        $report_generator = new Report_Assicurazioni_Report_Generator();
        $data = $report_generator->get_report_data($start_date, $end_date, $search);
        $stats = $report_generator->get_summary_stats($start_date, $end_date, $search);

        // Generate PDF using available method
        if (class_exists('TCPDF')) {
            $this->generate_tcpdf($data, $stats, $start_date, $end_date);
        } else {
            $this->generate_html_pdf($data, $stats, $start_date, $end_date);
        }
    }

    /**
     * Check if PDF support is available
     */
    private function check_pdf_support() {
        // Check for TCPDF (commonly available in WordPress environments)
        if (class_exists('TCPDF')) {
            return true;
        }

        // Check if we can use HTML to PDF conversion
        return true; // We'll provide a fallback HTML method
    }

    /**
     * Generate PDF using TCPDF
     */
    private function generate_tcpdf($data, $stats, $start_date, $end_date) {
        require_once(ABSPATH . 'wp-content/plugins/report-assicurazioni/vendor/tcpdf/tcpdf.php');
        
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Report Assicurazioni Plugin');
        $pdf->SetAuthor(get_bloginfo('name'));
        $pdf->SetTitle(__('Insurance Report', 'report-assicurazioni'));
        
        // Set margins
        $pdf->SetMargins(15, 20, 15);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(10);
        
        // Add a page
        $pdf->AddPage();
        
        // Set font
        $pdf->SetFont('helvetica', 'B', 16);
        
        // Title
        $pdf->Cell(0, 10, __('Report Assicurazioni', 'report-assicurazioni'), 0, 1, 'C');
        $pdf->Ln(5);
        
        // Date range
        $pdf->SetFont('helvetica', '', 12);
        if (!empty($start_date) || !empty($end_date)) {
            $date_range = '';
            if (!empty($start_date)) {
                $date_range .= __('From: ', 'report-assicurazioni') . date_i18n(get_option('date_format'), strtotime($start_date));
            }
            if (!empty($end_date)) {
                if (!empty($date_range)) $date_range .= ' ';
                $date_range .= __('To: ', 'report-assicurazioni') . date_i18n(get_option('date_format'), strtotime($end_date));
            }
            $pdf->Cell(0, 8, $date_range, 0, 1, 'L');
        }
        
        // Summary statistics
        $pdf->Ln(5);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, __('Summary', 'report-assicurazioni'), 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 6, __('Total Orders: ', 'report-assicurazioni') . $stats['total_orders'], 0, 1, 'L');
        $pdf->Cell(0, 6, __('Total Amount: ', 'report-assicurazioni') . $stats['clean_total_amount'], 0, 1, 'L');
        $pdf->Cell(0, 6, __('Average Order Value: ', 'report-assicurazioni') . $stats['clean_average_order_value'], 0, 1, 'L');
        
        // Table header
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(30, 8, __('Order #', 'report-assicurazioni'), 1, 0, 'C');
        $pdf->Cell(30, 8, __('Date', 'report-assicurazioni'), 1, 0, 'C');
        $pdf->Cell(100, 8, __('Products', 'report-assicurazioni'), 1, 0, 'C');
        $pdf->Cell(30, 8, __('Total', 'report-assicurazioni'), 1, 1, 'C');
        
        // Table data
        $pdf->SetFont('helvetica', '', 9);
        foreach ($data as $row) {
            $pdf->Cell(30, 8, $row['order_number'], 1, 0, 'C');
            $pdf->Cell(30, 8, $row['order_date'], 1, 0, 'C');
            $pdf->Cell(100, 8, substr($row['products'], 0, 60) . (strlen($row['products']) > 60 ? '...' : ''), 1, 0, 'L');
            $pdf->Cell(30, 8, $row['numeric_total'], 1, 1, 'R');
        }
        
        // Output PDF
        $filename = 'report-assicurazioni-' . date('Y-m-d-H-i-s') . '.pdf';
        $pdf->Output($filename, 'D');
        exit;
    }

    /**
     * Generate HTML PDF (fallback method)
     */
    private function generate_html_pdf($data, $stats, $start_date, $end_date) {
        // Set headers for PDF download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="report-assicurazioni-' . date('Y-m-d-H-i-s') . '.pdf"');
        
        // For now, we'll generate an HTML version that browsers can save as PDF
        // In a production environment, you might want to use a proper PDF library
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="report-assicurazioni-' . date('Y-m-d-H-i-s') . '.html"');
        
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title><?php _e('Report Assicurazioni', 'report-assicurazioni'); ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin-bottom: 30px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .text-center { text-align: center; }
                .text-right { text-align: right; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1><?php _e('Report Assicurazioni', 'report-assicurazioni'); ?></h1>
                <?php if (!empty($start_date) || !empty($end_date)) : ?>
                    <p>
                        <?php if (!empty($start_date)) : ?>
                            <?php _e('From: ', 'report-assicurazioni'); ?><?php echo date_i18n(get_option('date_format'), strtotime($start_date)); ?>
                        <?php endif; ?>
                        <?php if (!empty($end_date)) : ?>
                            <?php if (!empty($start_date)) echo ' '; ?>
                            <?php _e('To: ', 'report-assicurazioni'); ?><?php echo date_i18n(get_option('date_format'), strtotime($end_date)); ?>
                        <?php endif; ?>
                    </p>
                <?php endif; ?>
                <p><?php _e('Generated on: ', 'report-assicurazioni'); ?><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format')); ?></p>
            </div>

            <div class="summary">
                <h3><?php _e('Summary', 'report-assicurazioni'); ?></h3>
                <p><strong><?php _e('Total Orders: ', 'report-assicurazioni'); ?></strong><?php echo $stats['total_orders']; ?></p>
                <p><strong><?php _e('Total Amount: ', 'report-assicurazioni'); ?></strong><?php echo esc_html($stats['clean_total_amount']); ?></p>
                <p><strong><?php _e('Average Order Value: ', 'report-assicurazioni'); ?></strong><?php echo esc_html($stats['clean_average_order_value']); ?></p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th><?php _e('Order Number', 'report-assicurazioni'); ?></th>
                        <th><?php _e('Order Date', 'report-assicurazioni'); ?></th>
                        <th><?php _e('Products', 'report-assicurazioni'); ?></th>
                        <th class="text-right"><?php _e('Total Amount', 'report-assicurazioni'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data as $row) : ?>
                        <tr>
                            <td class="text-center">#<?php echo esc_html($row['order_number']); ?></td>
                            <td class="text-center"><?php echo esc_html($row['order_date']); ?></td>
                            <td><?php echo esc_html($row['products']); ?></td>
                            <td class="text-right"><?php echo esc_html($row['numeric_total']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <script class="no-print">
                // Auto-print when page loads
                window.onload = function() {
                    window.print();
                };
            </script>
        </body>
        </html>
        <?php
        exit;
    }
}
