<?php
/**
 * Plugin Name: Upsell nel Carrello
 * Description: Mostra fino a 3 prodotti upsell nella pagina del carrello, esclusi quelli già presenti nel carrello.
 * Version: 1.0
 * Author: <PERSON> Custom
 */

// Aggiunge gli upsell al carrello
add_action('woocommerce_cart_collaterals', 'upsell_nel_carrello_custom');

function upsell_nel_carrello_custom() {
    $carrello = WC()->cart->get_cart();
    $carrello_ids = [];
    $upsell_ids = [];

    foreach ($carrello as $item) {
        $product = $item['data'];
        $carrello_ids[] = $product->get_id();
        $upsells = $product->get_upsell_ids();
        if (!empty($upsells)) {
            $upsell_ids = array_merge($upsell_ids, $upsells);
        }
    }

    // Rimuovi duplicati e quelli già nel carrello
    $upsell_ids = array_unique(array_diff($upsell_ids, $carrello_ids));
    
    // Limita a massimo 3 prodotti
    $upsell_ids = array_slice($upsell_ids, 0, 3);

    if (!empty($upsell_ids)) {
        echo '<div class="upsell-carrello-custom"><h3>Ti potrebbero interessare:</h3>';
        echo do_shortcode('[products ids="' . implode(',', $upsell_ids) . '" columns="3" class="custom-upsell-layout"]');
        echo '</div>';
    }
}

// Carica il CSS personalizzato
add_action('wp_enqueue_scripts', 'upsell_carrello_custom_css');
function upsell_carrello_custom_css() {
    wp_enqueue_style('upsell-carrello-custom-style', plugin_dir_url(__FILE__) . 'style.css');
}
