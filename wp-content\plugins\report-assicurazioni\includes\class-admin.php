<?php
/**
 * Admin functionality for Report Assicurazioni plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Report_Assicurazioni_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_export_report_pdf', array($this, 'handle_pdf_export'));
        add_action('admin_init', array($this, 'handle_form_submission'));
    }

    /**
     * Add admin menu item
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=product',
            __('Report Assicurazioni', 'report-assicurazioni'),
            __('Report Assicurazioni', 'report-assicurazioni'),
            'manage_woocommerce',
            'report-assicurazioni',
            array($this, 'admin_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('product_page_report-assicurazioni' !== $hook) {
            return;
        }

        wp_enqueue_style(
            'report-assicurazioni-admin',
            REPORT_ASSICURAZIONI_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            REPORT_ASSICURAZIONI_VERSION
        );

        wp_enqueue_script(
            'report-assicurazioni-admin',
            REPORT_ASSICURAZIONI_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            REPORT_ASSICURAZIONI_VERSION,
            true
        );

        wp_localize_script('report-assicurazioni-admin', 'reportAssicurazioni', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('report_assicurazioni_nonce'),
            'strings' => array(
                'exporting' => __('Exporting...', 'report-assicurazioni'),
                'export_error' => __('Error exporting report. Please try again.', 'report-assicurazioni'),
                'loading' => __('Loading report data...', 'report-assicurazioni'),
                'date_error' => __('Start date cannot be later than end date.', 'report-assicurazioni'),
                'future_date_error' => __('Date cannot be in the future.', 'report-assicurazioni'),
                'export_success' => __('Export completed successfully.', 'report-assicurazioni'),
                'no_data' => __('No data available for export.', 'report-assicurazioni'),
            )
        ));
    }

    /**
     * Handle form submission
     */
    public function handle_form_submission() {
        if (!isset($_POST['report_assicurazioni_submit']) || !wp_verify_nonce($_POST['_wpnonce'], 'report_assicurazioni_action')) {
            return;
        }

        // Process form data here if needed
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        // Get current filters
        $start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : '';
        $end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : '';
        $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

        // Get report data
        $report_generator = new Report_Assicurazioni_Report_Generator();
        $orders = $report_generator->get_orders($start_date, $end_date, $search);

        ?>
        <div class="wrap">
            <h1><?php _e('Report Assicurazioni', 'report-assicurazioni'); ?></h1>
            
            <form method="get" class="report-filters">
                <input type="hidden" name="post_type" value="product">
                <input type="hidden" name="page" value="report-assicurazioni">
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="start_date"><?php _e('Start Date', 'report-assicurazioni'); ?></label>
                        </th>
                        <td>
                            <input type="date" id="start_date" name="start_date" value="<?php echo esc_attr($start_date); ?>" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="end_date"><?php _e('End Date', 'report-assicurazioni'); ?></label>
                        </th>
                        <td>
                            <input type="date" id="end_date" name="end_date" value="<?php echo esc_attr($end_date); ?>" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="search"><?php _e('Search', 'report-assicurazioni'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="search" name="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Search orders...', 'report-assicurazioni'); ?>" />
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(__('Filter Report', 'report-assicurazioni'), 'primary', 'filter_submit'); ?>
            </form>

            <div class="report-actions">
                <button type="button" id="export-pdf" class="button button-secondary">
                    <?php _e('Export to PDF', 'report-assicurazioni'); ?>
                </button>
            </div>

            <div class="report-table-container">
                <?php $this->display_report_table($orders); ?>
            </div>
        </div>
        <?php
    }

    /**
     * Display report table
     */
    private function display_report_table($orders) {
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Order Number', 'report-assicurazioni'); ?></th>
                    <th><?php _e('Order Date', 'report-assicurazioni'); ?></th>
                    <th><?php _e('Products', 'report-assicurazioni'); ?></th>
                    <th><?php _e('Total Amount', 'report-assicurazioni'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($orders)) : ?>
                    <?php foreach ($orders as $order) : ?>
                        <tr>
                            <td>
                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $order->get_id() . '&action=edit')); ?>">
                                    #<?php echo $order->get_order_number(); ?>
                                </a>
                            </td>
                            <td><?php echo $order->get_date_created()->date_i18n(get_option('date_format')); ?></td>
                            <td>
                                <?php
                                $products = array();
                                foreach ($order->get_items() as $item) {
                                    $product = $item->get_product();
                                    if ($product) {
                                        $products[] = $product->get_name() . ' (x' . $item->get_quantity() . ')';
                                    }
                                }
                                echo esc_html(implode(', ', $products));
                                ?>
                            </td>
                            <td><?php echo $order->get_formatted_order_total(); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="4"><?php _e('No orders found matching the criteria.', 'report-assicurazioni'); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * Handle PDF export AJAX request
     */
    public function handle_pdf_export() {
        check_ajax_referer('report_assicurazioni_nonce', 'nonce');

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'report-assicurazioni'));
        }

        $start_date = isset($_POST['start_date']) ? sanitize_text_field($_POST['start_date']) : '';
        $end_date = isset($_POST['end_date']) ? sanitize_text_field($_POST['end_date']) : '';
        $search = isset($_POST['search']) ? sanitize_text_field($_POST['search']) : '';

        $pdf_exporter = new Report_Assicurazioni_PDF_Exporter();
        $pdf_exporter->export($start_date, $end_date, $search);
    }
}
